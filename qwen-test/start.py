import dashscope_exec
import openai_exec


def log_start(desc):
    print(f'{"===" * 10} start {desc} {"===" * 10}')


def log_end(desc):
    print(f'{"===" * 10} end {desc} {"===" * 10}')


def log_openai(desc):
    print(f'exec [openai-{desc}]')


def log_dashscope(desc):
    print(f'exec [dashscope-{desc}]')


# api_key
dashscope_api_key = 'sk-5df0efe9051c4b8c8a2428de377466f3'

# model
models = {
    'qwen-plus': 'qwen-plus',
    'qwen-plus-latest': 'qwen-plus-latest',
    'qwen-plus-2025-07-28': 'qwen-plus-2025-07-28',
    'qwen-plus-2025-07-14': 'qwen-plus-2025-07-14',
    'qwen-plus-2025-04-28': 'qwen-plus-2025-04-28',
    'qwen-max': 'qwen-max',
    'qwen-max-latest': 'qwen-max-latest',
    'qwen-turbo': 'qwen-turbo',
    'qwen-turbo-latest': 'qwen-turbo-latest',
    'qwen3-coder-plus': 'qwen3-coder-plus',
    'qwen3-coder-plus-2025-07-22': 'qwen3-coder-plus-2025-07-22',
    'qwen3-coder-480b-a35b-instruct': 'qwen3-coder-480b-a35b-instruct',
}

# system
content_system = {'role': 'system', 'content': 'You are a helpful assistant.'}


def __chat__():
    # chat
    __desc__ = 'chat'
    log_start(__desc__)

    # model
    __model__ = models['qwen-max']
    # content
    __content_user_chat__ = {'role': 'user', 'content': '你是谁？'}
    # messages
    __messages_chat__ = [
        content_system,
        __content_user_chat__
    ]

    log_openai(__desc__)
    openai_exec.__chat__(dashscope_api_key, __model__, __messages_chat__)
    log_dashscope(__desc__)
    dashscope_exec.__chat__(dashscope_api_key, __model__, __messages_chat__)

    log_end(__desc__)


def __async_chat__():
    __desc__ = 'async-chat'
    log_start(__desc__)

    # model
    __model__ = models['qwen-max']
    # questions
    __questions__ = ["你是谁？", "你会什么？", "天气怎么样？"]

    log_openai(__desc__)
    openai_exec.__async_chat__(dashscope_api_key, __model__, __questions__)
    log_dashscope(__desc__)
    dashscope_exec.__async_chat__(dashscope_api_key, __model__, __questions__)

    log_end(__desc__)


def __multi_cycle_chat__():
    __desc__ = 'multi-cycle-chat'
    log_start(__desc__)

    # model
    __model__ = models['qwen-max']
    # messages
    __messages__ = [
        {
            "role": "system",
            "content": """你是一名阿里云百炼手机商店的店员，你负责给用户推荐手机。手机有两个参数：屏幕尺寸（包括6.1英寸、6.5英寸、6.7英寸）、分辨率（包括2K、4K）。
        你一次只能向用户提问一个参数。如果用户提供的信息不全，你需要反问他，让他提供没有提供的参数。如果参数收集完成，你要说：我已了解您的购买意向，请稍等。""",
        }
    ]
    # assistant_output
    __assistant_output__ = '欢迎光临阿里云百炼手机商店，您需要购买什么尺寸的手机呢？'
    # assistant_finish
    __assistant_finish__ = '我已了解您的购买意向'

    log_openai(__desc__)
    openai_exec.__multi_cycle_chat__(dashscope_api_key,
                                     __model__,
                                     __messages__,
                                     __assistant_output__,
                                     __assistant_finish__)

    log_dashscope(__desc__)
    dashscope_exec.__multi_cycle_chat__(dashscope_api_key,
                                        __model__,
                                        __messages__,
                                        __assistant_output__,
                                        __assistant_finish__)

    log_end(__desc__)


def __stream_chat__():
    __desc__ = 'stream-chat'
    log_start(__desc__)

    __model__ = models['qwen-plus']

    __messages__ = [
        content_system,
        {'role': 'user', 'content': '你是谁？'}
    ]

    # log_openai(__desc__)
    # openai_exec.__stream_chat__(dashscope_api_key, __model__, __messages__)
    log_dashscope(__desc__)
    dashscope_exec.__stream_chat__(dashscope_api_key, __model__, __messages__)

    log_end(__desc__)
