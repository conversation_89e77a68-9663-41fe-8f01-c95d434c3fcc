import asyncio
import platform

from openai import AsyncOpenAI
from openai import OpenAI

reference = "https://help.aliyun.com/zh/model-studio/developer-reference/error-code"
dashscope_base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"


def __client__(__api_key__, __base_url__):
    # 初始化client
    client = OpenAI(
        api_key=__api_key__,
        base_url=__base_url__,
    )

    return client


def log_error(error):
    # 记录错误信息
    print(f"错误信息：{error}")
    print(f"请参考文档：{reference}")


def __completion__(__api_key__, __model__, __messages__, __stream__=False):
    # 初始化client
    client = __client__(__api_key__, dashscope_base_url)
    # 初始化completion
    completion = client.chat.completions.create(
        model=__model__,
        messages=__messages__,
        stream=__stream__,
    )

    return completion


def __chat__(__api_key__, __model__, __messages__):
    try:
        completion = __completion__(__api_key__, __model__, __messages__)
        print(completion.choices[0].message.content)
    except Exception as e:
        log_error(e)


def __async_client__(__api_key__, __base_url__):
    # 初始化async_client
    async_client = AsyncOpenAI(
        api_key=__api_key__,
        base_url=__base_url__
    )

    return async_client


async def task(__api_key__, __base_url__, __model__, __question__):
    async_client = __async_client__(__api_key__, __base_url__)

    response = await async_client.chat.completions.create(
        messages=[{'role': 'user', 'content': __question__}],
        model=__model__,
    )
    print(f'openai answer: {response.choices[0].message.content}')


async def __async_chat_main__(__api_key__, __model__, __questions__):
    tasks = [task(__api_key__, dashscope_base_url, __model__, q) for q in __questions__]
    await asyncio.gather(*tasks)


def __async_chat__(__api_key__, __model__, __questions__):
    if platform.system() == "Windows":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(__async_chat_main__(__api_key__, __model__, __questions__), debug=False)


def __multi_cycle_chat__(__api_key__, __model__, __messages__, __assistant_output__, __assistant_finish__):
    loop = 0
    print(f'openai 开始轮训对话 模型输出：{__assistant_output__}')

    while __assistant_finish__ not in __assistant_output__:
        user_input = input('openai 请输入：')
        # 添加用户信息到用户 messages
        __messages__.append({'role': 'user', 'content': user_input})
        __assistant_output__ = __completion__(__api_key__, __model__, __messages__).choices[0].message.content
        # 添加模型信息到助手 messages
        __messages__.append({'role': 'assistant', 'content': __assistant_output__})
        loop += 1
        print(f'openai 第{loop}轮对话 模型输出：{__assistant_output__}')
        print('\n')


def __stream_chat__(__api_key__, __model__, __messages__):
    completion = __completion__(__api_key__, __model__, __messages__, True)

    full_content = ''
    print('openai stream 输出内容:')
    for chunk in completion:
        if chunk.choices:
            chunk_content = chunk.choices[0].delta.content
            if chunk_content:
                print(f'openai chunk_content: {chunk_content}\n', end='')
                full_content += chunk_content

    print(f'\nopenai full_content: {full_content}')
