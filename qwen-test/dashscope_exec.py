import asyncio
import platform

from dashscope import Generation
from dashscope.aigc.generation import AioGeneration

reference = "https://help.aliyun.com/zh/model-studio/developer-reference/error-code"


def build_response(__api_key__, __model__, __messages__, __stream__=False, __incremental_output__=False):
    # 构建 response
    response = Generation.call(
        api_key=__api_key__,
        model=__model__,
        messages=__messages__,
        result_format="message",
        stream=__stream__,
        # 增量式流式输出
        incremental_output=__incremental_output__,
        # Qwen3模型通过enable_thinking参数控制思考过程（开源版默认True，商业版默认False）
        # 使用Qwen3开源版模型时，若未启用流式输出，请将下行取消注释，否则会
    )
    return response


def log_failure_response(response):
    # 记录失败信息
    print(f"失败信息：{response.message}")
    print(f"HTTP返回码：{response.status_code}")
    print(f"错误码：{response.code}")
    print(f"请参考文档：{reference}")


def log_error(error):
    # 记录错误信息
    print(f"错误信息：{error}")
    print(f"请参考文档：{reference}")


def __chat__(__api_key__, __model__, __messages__):
    try:
        # 调用 dashscope 接口
        response = build_response(__api_key__, __model__, __messages__)
        if response.status_code == 200:
            print(response.output.choices[0].message.content)
        else:
            log_failure_response(response)
    except Exception as e:
        log_error(e)


async def task(__api_key__, __model__, __question__):
    print(f"dashscope question：{__question__}")
    response = await AioGeneration.call(
        api_key=__api_key__,
        model=__model__,
        # deprecated 这种写法运行结果等价于 prompt 但是实际查看文档显示已废弃
        # messages=[{'role': 'user', 'content': __question__}],
        prompt=__question__,
    )
    print(f'dashscope answer：{response.output.text}')


async def __async_chat_main__(__api_key__, __model__, __questions__):
    tasks = [task(__api_key__, __model__, q) for q in __questions__]
    await asyncio.gather(*tasks)


def __async_chat__(__api_key__, __model__, __questions__):
    if platform.system() == "Windows":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    asyncio.run(__async_chat_main__(__api_key__, __model__, __questions__))


def __multi_cycle_chat__(__api_key__, __model__, __messages__, __assistant_output__, __assistant_finish__):
    loop = 0
    print(f'dashscope 开始轮训对话 模型输出：{__assistant_output__}')

    while __assistant_finish__ not in __assistant_output__:
        user_input = input('dashscope 请输入：')
        # 添加用户信息到用户 messages
        __messages__.append({'role': 'user', 'content': user_input})
        __assistant_output__ = build_response(__api_key__, __model__, __messages__).output.choices[
            0].message.content
        # 添加模型信息到助手 messages
        __messages__.append({'role': 'assistant', 'content': __assistant_output__})
        loop += 1
        print(f'dashscope 第{loop}轮对话 模型输出：{__assistant_output__}')
        print('\n')


def __stream_chat__(__api_key__, __model__, __messages__):
    responses = build_response(__api_key__, __model__, __messages__, True, True)
    full_content = ''
    print('dashscope stream 输出内容:')
    for response in responses:
        chunk_content = response.output.choices[0].message.content
        full_content += chunk_content
        print(f'dashscope chunk_content: {chunk_content}')

    print(f'\ndashscope full_content: {full_content}')
